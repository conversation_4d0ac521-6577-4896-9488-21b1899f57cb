{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/lib/api.ts"], "sourcesContent": ["import { Meme, CreateMemeRequest, SearchQuery, ApiResponse, UploadMemeFormData } from '@/types/meme';\r\n\r\nconst API_BASE_URL = 'http://127.0.0.1:3001';\r\n\r\n// Generic API function with error handling\r\nasync function apiCall<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        ...options?.headers,\r\n      },\r\n      ...options,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('API call failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Health check\r\nexport async function healthCheck(): Promise<ApiResponse<string>> {\r\n  return apiCall<string>('/');\r\n}\r\n\r\n// Get all memes\r\nexport async function getMemes(): Promise<ApiResponse<Meme[]>> {\r\n  return apiCall<Meme[]>('/api/memes');\r\n}\r\n\r\n// Get meme by ID\r\nexport async function getMemeById(id: string): Promise<ApiResponse<Meme>> {\r\n  return apiCall<Meme>(`/api/memes/${id}`);\r\n}\r\n\r\n// Create meme with JSON\r\nexport async function createMeme(meme: CreateMemeRequest): Promise<ApiResponse<Meme>> {\r\n  return apiCall<Meme>('/api/memes', {\r\n    method: 'POST',\r\n    body: JSON.stringify(meme),\r\n  });\r\n}\r\n\r\n// Upload meme with file\r\nexport async function uploadMeme(formData: UploadMemeFormData): Promise<ApiResponse<Meme>> {\r\n  const form = new FormData();\r\n  \r\n  if (formData.title) {\r\n    form.append('title', formData.title);\r\n  }\r\n  form.append('tags', formData.tags);\r\n  form.append('image', formData.image);\r\n\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api/memes/upload`, {\r\n      method: 'POST',\r\n      body: form,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Upload failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Delete meme\r\nexport async function deleteMeme(id: string): Promise<ApiResponse<string>> {\r\n  return apiCall<string>(`/api/memes/${id}`, {\r\n    method: 'DELETE',\r\n  });\r\n}\r\n\r\n// Search memes\r\nexport async function searchMemes(query: SearchQuery): Promise<ApiResponse<Meme[]>> {\r\n  const params = new URLSearchParams();\r\n  \r\n  if (query.q) params.append('q', query.q);\r\n  if (query.tag) params.append('tag', query.tag);\r\n  if (query.title) params.append('title', query.title);\r\n  if (query.limit) params.append('limit', query.limit.toString());\r\n\r\n  return apiCall<Meme[]>(`/api/memes/search?${params.toString()}`);\r\n}\r\n\r\n// Get all tags\r\nexport async function getAllTags(): Promise<ApiResponse<string[]>> {\r\n  return apiCall<string[]>('/api/tags');\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA,MAAM,eAAe;AAErB,2CAA2C;AAC3C,eAAe,QAAW,QAAgB,EAAE,OAAqB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAiB,OAAf,cAAwB,OAAT,WAAY;YACzD,SAAS;gBACP,gBAAgB;mBACb,oBAAA,8BAAA,QAAS,OAAO,AAAnB;YACF;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,MAAM;IACR;AACF;AAGO,eAAe;IACpB,OAAO,QAAgB;AACzB;AAGO,eAAe;IACpB,OAAO,QAAgB;AACzB;AAGO,eAAe,YAAY,EAAU;IAC1C,OAAO,QAAc,AAAC,cAAgB,OAAH;AACrC;AAGO,eAAe,WAAW,IAAuB;IACtD,OAAO,QAAc,cAAc;QACjC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAGO,eAAe,WAAW,QAA4B;IAC3D,MAAM,OAAO,IAAI;IAEjB,IAAI,SAAS,KAAK,EAAE;QAClB,KAAK,MAAM,CAAC,SAAS,SAAS,KAAK;IACrC;IACA,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI;IACjC,KAAK,MAAM,CAAC,SAAS,SAAS,KAAK;IAEnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,cAAa,sBAAoB;YAC/D,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,MAAM;IACR;AACF;AAGO,eAAe,WAAW,EAAU;IACzC,OAAO,QAAgB,AAAC,cAAgB,OAAH,KAAM;QACzC,QAAQ;IACV;AACF;AAGO,eAAe,YAAY,KAAkB;IAClD,MAAM,SAAS,IAAI;IAEnB,IAAI,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;IACvC,IAAI,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG;IAC7C,IAAI,MAAM,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,MAAM,KAAK;IACnD,IAAI,MAAM,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;IAE5D,OAAO,QAAgB,AAAC,qBAAsC,OAAlB,OAAO,QAAQ;AAC7D;AAGO,eAAe;IACpB,OAAO,QAAkB;AAC3B", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/lib/utils.ts"], "sourcesContent": ["// Utility functions for the meme app\r\n\r\nexport function formatDate(dateString: string): string {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  } catch {\r\n    return 'Invalid date';\r\n  }\r\n}\r\n\r\nexport function formatTags(tags: string[]): string {\r\n  return tags.map(tag => `#${tag}`).join(' ');\r\n}\r\n\r\nexport function parseTags(tagsString: string): string[] {\r\n  return tagsString\r\n    .split(',')\r\n    .map(tag => tag.trim())\r\n    .filter(tag => tag.length > 0);\r\n}\r\n\r\nexport function getImageUrl(imagePath: string): string {\r\n  // If it's already a full URL, return as is\r\n  if (imagePath.startsWith('http')) {\r\n    return imagePath;\r\n  }\r\n  // If it's a backend path, prepend the API base URL\r\n  return `http://127.0.0.1:3001${imagePath}`;\r\n}\r\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;AAE9B,SAAS,WAAW,UAAkB;IAC3C,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,WAAW,IAAc;IACvC,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,AAAC,IAAO,OAAJ,MAAO,IAAI,CAAC;AACzC;AAEO,SAAS,UAAU,UAAkB;IAC1C,OAAO,WACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;AAChC;AAEO,SAAS,YAAY,SAAiB;IAC3C,2CAA2C;IAC3C,IAAI,UAAU,UAAU,CAAC,SAAS;QAChC,OAAO;IACT;IACA,mDAAmD;IACnD,OAAO,AAAC,wBAAiC,OAAV;AACjC", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/components/FullScreenMeme.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Meme } from '@/types/meme';\r\nimport { formatDate, getImageUrl } from '@/lib/utils';\r\nimport { X, Calendar, Tag, Download, Share2 } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport { useEffect } from 'react';\r\n\r\ninterface FullScreenMemeProps {\r\n  meme: Meme;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport default function FullScreenMeme({ meme, isOpen, onClose }: FullScreenMemeProps) {\r\n  // Handle escape key to close\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const handleDownload = () => {\r\n    const imageUrl = getImageUrl(meme.image_url);\r\n    const link = document.createElement('a');\r\n    link.href = imageUrl;\r\n    link.download = `meme-${meme.id}.jpg`;\r\n    link.target = '_blank';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  const handleShare = async () => {\r\n    if (navigator.share) {\r\n      try {\r\n        await navigator.share({\r\n          title: meme.title || 'Check out this meme!',\r\n          text: meme.title || 'Awesome meme from the public database',\r\n          url: window.location.href,\r\n        });\r\n      } catch {\r\n        // User cancelled or share failed\r\n        copyToClipboard();\r\n      }\r\n    } else {\r\n      copyToClipboard();\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = () => {\r\n    navigator.clipboard.writeText(window.location.href).then(() => {\r\n      // Could add a toast notification here\r\n      alert('Link copied to clipboard!');\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 bg-black/95 backdrop-blur-sm\">\r\n      {/* Header */}\r\n      <div className=\"absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent\">\r\n        <div className=\"flex items-center justify-between p-4 md:p-6\">\r\n          <div className=\"flex items-center gap-4\">\r\n            {meme.title && (\r\n              <h2 className=\"text-white text-lg md:text-xl font-semibold max-w-md truncate\">\r\n                {meme.title}\r\n              </h2>\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"flex items-center gap-2\">\r\n            <button\r\n              onClick={handleShare}\r\n              className=\"p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors\"\r\n              title=\"Share meme\"\r\n            >\r\n              <Share2 size={20} />\r\n            </button>\r\n            <button\r\n              onClick={handleDownload}\r\n              className=\"p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors\"\r\n              title=\"Download meme\"\r\n            >\r\n              <Download size={20} />\r\n            </button>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors\"\r\n              title=\"Close (Esc)\"\r\n            >\r\n              <X size={24} />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div \r\n        className=\"flex items-center justify-center min-h-screen p-4 md:p-8\"\r\n        onClick={onClose}\r\n      >\r\n        <div \r\n          className=\"relative max-w-full max-h-full\"\r\n          onClick={(e) => e.stopPropagation()}\r\n        >\r\n          <Image\r\n            src={getImageUrl(meme.image_url)}\r\n            alt={meme.title || 'Meme'}\r\n            width={1200}\r\n            height={800}\r\n            className=\"max-w-full max-h-[calc(100vh-8rem)] object-contain rounded-lg shadow-2xl\"\r\n            onError={(e) => {\r\n              e.currentTarget.src = '/placeholder-meme.svg';\r\n            }}\r\n            priority\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <div className=\"absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/50 to-transparent\">\r\n        <div className=\"p-4 md:p-6\">\r\n          <div className=\"flex flex-wrap items-center justify-between gap-4\">\r\n            {/* Tags */}\r\n            {meme.tags.length > 0 && (\r\n              <div className=\"flex items-center gap-2\">\r\n                <Tag size={16} className=\"text-blue-400 flex-shrink-0\" />\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {meme.tags.map((tag, index) => (\r\n                    <span \r\n                      key={index} \r\n                      className=\"px-2 py-1 bg-blue-500/20 text-blue-200 text-sm rounded-md font-medium border border-blue-400/30\"\r\n                    >\r\n                      #{tag}\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Date */}\r\n            <div className=\"flex items-center gap-2 text-white/70\">\r\n              <Calendar size={16} />\r\n              <span className=\"text-sm\">{formatDate(meme.created_at)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;AAce,SAAS,eAAe,KAA8C;QAA9C,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAuB,GAA9C;;IACrC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;4CAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;mCAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;QAC3C,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,AAAC,QAAe,OAAR,KAAK,EAAE,EAAC;QAChC,KAAK,MAAM,GAAG;QACd,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,KAAK,KAAK,IAAI;oBACrB,MAAM,KAAK,KAAK,IAAI;oBACpB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,UAAM;gBACN,iCAAiC;gBACjC;YACF;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;YACvD,sCAAsC;YACtC,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,KAAK,kBACT,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAEhB,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;8CAElB,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjB,6LAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEjC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;wBAC/B,KAAK,KAAK,KAAK,IAAI;wBACnB,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,aAAa,CAAC,GAAG,GAAG;wBACxB;wBACA,QAAQ;;;;;;;;;;;;;;;;0BAMd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACzB,6LAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;gDAEC,WAAU;;oDACX;oDACG;;+CAHG;;;;;;;;;;;;;;;;0CAWf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAW,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnE;GAvJwB;KAAA", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/components/MemeCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Meme } from '@/types/meme';\r\nimport { formatDate, getImageUrl } from '@/lib/utils';\r\nimport { Trash2, Eye } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport FullScreenMeme from './FullScreenMeme';\r\n\r\ninterface MemeCardProps {\r\n  meme: Meme;\r\n  onDelete?: (id: string) => void;\r\n}\r\n\r\nexport default function MemeCard({ meme, onDelete }: MemeCardProps) {\r\n  const [isFullScreenOpen, setIsFullScreenOpen] = useState(false);\r\n\r\n  const handleDelete = () => {\r\n    if (onDelete) {\r\n      onDelete(meme.id);\r\n    }\r\n  };\r\n\r\n  const handleImageClick = () => {\r\n    setIsFullScreenOpen(true);\r\n  };\r\n\r\n  const handleCloseFullScreen = () => {\r\n    setIsFullScreenOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-100\">\r\n        {/* Image Container */}\r\n        <div \r\n          className=\"relative aspect-square overflow-hidden cursor-pointer bg-gray-50\"\r\n          onClick={handleImageClick}\r\n        >\r\n          <Image\r\n            src={getImageUrl(meme.image_url)}\r\n            alt={meme.title || 'Meme'}\r\n            fill\r\n            className=\"object-cover group-hover:scale-[1.02] transition-transform duration-200\"\r\n            sizes=\"(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw\"\r\n            onError={(e) => {\r\n              e.currentTarget.src = '/placeholder-meme.svg';\r\n            }}\r\n          />\r\n          \r\n          {/* Hover Overlay */}\r\n          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200\" />\r\n          \r\n          {/* Action Buttons */}\r\n          <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n            <button\r\n              onClick={handleImageClick}\r\n              className=\"bg-white/95 hover:bg-white text-gray-700 hover:text-gray-900 p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110\"\r\n              title=\"View full size\"\r\n            >\r\n              <Eye size={18} />\r\n            </button>\r\n          </div>\r\n          \r\n          {/* Delete Button */}\r\n          {onDelete && (\r\n            <button\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                handleDelete();\r\n              }}\r\n              className=\"absolute top-2 right-2 bg-red-500/90 hover:bg-red-600 text-white p-1.5 rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-md hover:scale-110\"\r\n              title=\"Delete meme\"\r\n            >\r\n              <Trash2 size={14} />\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Content - Only show if title exists or has meaningful info */}\r\n        {(meme.title || meme.tags.length > 0) && (\r\n          <div className=\"p-3\">\r\n            {/* Title */}\r\n            {meme.title && (\r\n              <h3 className=\"text-sm font-medium text-gray-900 mb-2 line-clamp-2\">\r\n                {meme.title}\r\n              </h3>\r\n            )}\r\n\r\n            {/* Tags - Show max 2 tags */}\r\n            {meme.tags.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-1 mb-2\">\r\n                {meme.tags.slice(0, 2).map((tag, index) => (\r\n                  <span key={index} className=\"inline-block px-2 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-md font-medium\">\r\n                    #{tag}\r\n                  </span>\r\n                ))}\r\n                {meme.tags.length > 2 && (\r\n                  <span className=\"inline-block px-2 py-0.5 bg-gray-50 text-gray-500 text-xs rounded-md\">\r\n                    +{meme.tags.length - 2}\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Date - Simplified */}\r\n            <div className=\"text-xs text-gray-400\">\r\n              {formatDate(meme.created_at)}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Full Screen Modal */}\r\n      <FullScreenMeme \r\n        meme={meme}\r\n        isOpen={isFullScreenOpen}\r\n        onClose={handleCloseFullScreen}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;AAce,SAAS,SAAS,KAAiC;QAAjC,EAAE,IAAI,EAAE,QAAQ,EAAiB,GAAjC;;IAC/B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,KAAK,EAAE;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;IACtB;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;gCAC/B,KAAK,KAAK,KAAK,IAAI;gCACnB,IAAI;gCACJ,WAAU;gCACV,OAAM;gCACN,SAAS,CAAC;oCACR,EAAE,aAAa,CAAC,GAAG,GAAG;gCACxB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;;;;;;4BAKd,0BACC,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;;;;;;;oBAMnB,CAAC,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,mBAClC,6LAAC;wBAAI,WAAU;;4BAEZ,KAAK,KAAK,kBACT,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAKd,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;4CAAiB,WAAU;;gDAAmF;gDAC3G;;2CADO;;;;;oCAIZ,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;wCAAK,WAAU;;4CAAuE;4CACnF,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;0CAO7B,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;0BAOnC,6LAAC,uIAAA,CAAA,UAAc;gBACb,MAAM;gBACN,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GA3GwB;KAAA", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/lib/autoTag.ts"], "sourcesContent": ["// AI-powered auto-tagging utility for memes\r\nexport interface AutoTagResult {\r\n  tags: string[];\r\n  confidence: number;\r\n  category?: string;\r\n  description?: string;\r\n}\r\n\r\n// Convert image file to base64 for API\r\nasync function fileToBase64(file: File): Promise<string> {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64 = reader.result as string;\r\n      // Remove data:image/jpeg;base64, prefix\r\n      resolve(base64.split(',')[1]);\r\n    };\r\n    reader.onerror = reject;\r\n    reader.readAsDataURL(file);\r\n  });\r\n}\r\n\r\n// AI-powered image analysis\r\nasync function analyzeImageWithAI(file: File, title?: string): Promise<AutoTagResult> {\r\n  try {\r\n    const base64Image = await fileToBase64(file);\r\n    \r\n    const response = await fetch('/api/analyze-meme', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        image: base64Image,\r\n        title: title || '',\r\n        mimeType: file.type\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API request failed: ${response.status}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    return {\r\n      tags: result.tags || [],\r\n      confidence: result.confidence || 0.9,\r\n      category: result.category,\r\n      description: result.description\r\n    };\r\n  } catch (error) {\r\n    console.error('AI analysis failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Common meme patterns and keywords\r\nconst MEME_PATTERNS = {\r\n  // Text-based detection patterns\r\n  textPatterns: [\r\n    { pattern: /\\b(lol|lmao|rofl|haha|funny)\\b/i, tags: ['funny', 'humor'] },\r\n    { pattern: /\\b(cat|kitten|kitty|feline)\\b/i, tags: ['cat', 'animal'] },\r\n    { pattern: /\\b(dog|puppy|doggo|pupper)\\b/i, tags: ['dog', 'animal'] },\r\n    { pattern: /\\b(reaction|mood|feel|when)\\b/i, tags: ['reaction'] },\r\n    { pattern: /\\b(fails?|epic fail|oops)\\b/i, tags: ['fail', 'funny'] },\r\n    { pattern: /\\b(drake|pointing|choose)\\b/i, tags: ['drake', 'choice'] },\r\n    { pattern: /\\b(stonks|money|profit)\\b/i, tags: ['stonks', 'finance'] },\r\n    { pattern: /\\b(surprised|shocked|pikachu)\\b/i, tags: ['surprised', 'reaction'] },\r\n    { pattern: /\\b(distracted|boyfriend|girlfriend)\\b/i, tags: ['distracted-boyfriend'] },\r\n    { pattern: /\\b(this is fine|fire|burning)\\b/i, tags: ['this-is-fine', 'stress'] },\r\n    { pattern: /\\b(brain|galaxy|expanding)\\b/i, tags: ['expanding-brain', 'smart'] },\r\n    { pattern: /\\b(change my mind|prove me wrong)\\b/i, tags: ['change-my-mind', 'debate'] },\r\n    { pattern: /\\b(wojak|doomer|bloomer|coomer)\\b/i, tags: ['wojak'] },\r\n    { pattern: /\\b(pepe|frog|rare pepe)\\b/i, tags: ['pepe', 'frog'] },\r\n    { pattern: /\\b(chad|virgin|vs)\\b/i, tags: ['chad', 'virgin-vs-chad'] },\r\n    { pattern: /\\b(gaming|gamer|game)\\b/i, tags: ['gaming'] },\r\n    { pattern: /\\b(programming|code|developer|bug)\\b/i, tags: ['programming', 'tech'] },\r\n    { pattern: /\\b(work|office|boss|job)\\b/i, tags: ['work', 'office'] },\r\n    { pattern: /\\b(school|student|teacher|exam)\\b/i, tags: ['school', 'education'] },\r\n    { pattern: /\\b(weekend|monday|friday)\\b/i, tags: ['weekend', 'relatable'] },\r\n    { pattern: /\\b(covid|pandemic|mask|vaccine)\\b/i, tags: ['covid', 'pandemic'] },\r\n    { pattern: /\\b(2024|2025|new year)\\b/i, tags: ['current-year'] },\r\n  ],\r\n  \r\n  // File name patterns\r\n  filenamePatterns: [\r\n    { pattern: /funny/i, tags: ['funny'] },\r\n    { pattern: /cat/i, tags: ['cat'] },\r\n    { pattern: /dog/i, tags: ['dog'] },\r\n    { pattern: /reaction/i, tags: ['reaction'] },\r\n    { pattern: /drake/i, tags: ['drake'] },\r\n    { pattern: /stonks/i, tags: ['stonks'] },\r\n    { pattern: /pikachu/i, tags: ['pikachu', 'surprised'] },\r\n    { pattern: /wojak/i, tags: ['wojak'] },\r\n    { pattern: /pepe/i, tags: ['pepe'] },\r\n    { pattern: /chad/i, tags: ['chad'] },\r\n  ],\r\n\r\n  // Common meme formats/templates\r\n  templates: [\r\n    'drake-pointing',\r\n    'distracted-boyfriend',\r\n    'expanding-brain',\r\n    'change-my-mind',\r\n    'this-is-fine',\r\n    'surprised-pikachu',\r\n    'stonks',\r\n    'wojak',\r\n    'pepe',\r\n    'chad-vs-virgin',\r\n    'galaxy-brain',\r\n    'two-buttons',\r\n    'epic-handshake',\r\n    'woman-yelling-at-cat',\r\n    'always-has-been',\r\n  ]\r\n};\r\n\r\n// Popular tag suggestions based on current trends\r\nconst TRENDING_TAGS = [\r\n  'relatable',\r\n  'mood',\r\n  'funny',\r\n  'reaction',\r\n  'wholesome',\r\n  'cursed',\r\n  'blessed',\r\n  'cringe',\r\n  'based',\r\n  'sus',\r\n  'meta',\r\n  'deep-fried',\r\n  'low-effort',\r\n  'high-quality',\r\n  'original',\r\n  'template',\r\n];\r\n\r\nexport async function analyzeImageForTags(file: File, title?: string): Promise<AutoTagResult> {\r\n  try {\r\n    // Try AI analysis first\r\n    return await analyzeImageWithAI(file, title);\r\n  } catch (error) {\r\n    console.warn('AI analysis failed, using fallback:', error);\r\n    // Fall back to pattern matching\r\n    return fallbackAnalysis(file, title);\r\n  }\r\n}\r\n\r\n// Fallback analysis using pattern matching\r\nfunction fallbackAnalysis(file: File, title?: string): AutoTagResult {\r\n  const tags = new Set<string>();\r\n  let confidence = 0.3;\r\n\r\n  // Analyze filename\r\n  const filename = file.name.toLowerCase();\r\n  MEME_PATTERNS.filenamePatterns.forEach(({ pattern, tags: patternTags }) => {\r\n    if (pattern.test(filename)) {\r\n      patternTags.forEach(tag => tags.add(tag));\r\n      confidence += 0.2;\r\n    }\r\n  });\r\n\r\n  // Analyze title if provided\r\n  if (title) {\r\n    const titleLower = title.toLowerCase();\r\n    MEME_PATTERNS.textPatterns.forEach(({ pattern, tags: patternTags }) => {\r\n      if (pattern.test(titleLower)) {\r\n        patternTags.forEach(tag => tags.add(tag));\r\n        confidence += 0.3;\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add some trending tags based on current date/context\r\n  const currentYear = new Date().getFullYear();\r\n  if (title?.includes(currentYear.toString()) || filename.includes(currentYear.toString())) {\r\n    tags.add('current-year');\r\n    confidence += 0.1;\r\n  }\r\n\r\n  // Always ensure we have at least some basic tags\r\n  if (tags.size === 0) {\r\n    tags.add('funny');\r\n    tags.add('relatable');\r\n    confidence = 0.5;\r\n  }\r\n\r\n  // Limit to most relevant tags (max 3 for optimal tagging)\r\n  const finalTags = Array.from(tags).slice(0, 3);\r\n  \r\n  return {\r\n    tags: finalTags,\r\n    confidence: Math.min(confidence, 1.0),\r\n    category: 'pattern_matched'\r\n  };\r\n}\r\n\r\nexport function suggestAdditionalTags(existingTags: string[]): string[] {\r\n  const suggestions = new Set<string>();\r\n  const existingSet = new Set(existingTags.map(t => t.toLowerCase()));\r\n\r\n  // Suggest related tags based on existing ones\r\n  existingTags.forEach(tag => {\r\n    const tagLower = tag.toLowerCase();\r\n    \r\n    // Category-based suggestions\r\n    if (['cat', 'dog', 'animal'].some(t => tagLower.includes(t))) {\r\n      ['cute', 'pet', 'wholesome'].forEach(s => {\r\n        if (!existingSet.has(s)) suggestions.add(s);\r\n      });\r\n    }\r\n    \r\n    if (['funny', 'humor', 'lol'].some(t => tagLower.includes(t))) {\r\n      ['comedy', 'hilarious', 'joke'].forEach(s => {\r\n        if (!existingSet.has(s)) suggestions.add(s);\r\n      });\r\n    }\r\n    \r\n    if (['gaming', 'game'].some(t => tagLower.includes(t))) {\r\n      ['gamer', 'videogame', 'esports'].forEach(s => {\r\n        if (!existingSet.has(s)) suggestions.add(s);\r\n      });\r\n    }\r\n    \r\n    if (['work', 'office', 'job'].some(t => tagLower.includes(t))) {\r\n      ['relatable', 'monday', 'stress'].forEach(s => {\r\n        if (!existingSet.has(s)) suggestions.add(s);\r\n      });\r\n    }\r\n  });\r\n\r\n  // Add some trending suggestions if we don't have many\r\n  if (suggestions.size < 2) {\r\n    TRENDING_TAGS.forEach(tag => {\r\n      if (!existingSet.has(tag) && suggestions.size < 3) {\r\n        suggestions.add(tag);\r\n      }\r\n    });\r\n  }\r\n\r\n  return Array.from(suggestions).slice(0, 3);\r\n}\r\n\r\n// Quick tag suggestions for empty state\r\nexport function getQuickTagSuggestions(): string[] {\r\n  return [\r\n    'funny', 'relatable', 'mood', 'reaction', 'wholesome',\r\n    'cat', 'dog', 'gaming', 'work', 'school'\r\n  ];\r\n}\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;AAQ5C,uCAAuC;AACvC,eAAe,aAAa,IAAU;IACpC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,wCAAwC;YACxC,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9B;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,aAAa,CAAC;IACvB;AACF;AAEA,4BAA4B;AAC5B,eAAe,mBAAmB,IAAU,EAAE,KAAc;IAC1D,IAAI;QACF,MAAM,cAAc,MAAM,aAAa;QAEvC,MAAM,WAAW,MAAM,MAAM,qBAAqB;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;gBACP,OAAO,SAAS;gBAChB,UAAU,KAAK,IAAI;YACrB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YACL,MAAM,OAAO,IAAI,IAAI,EAAE;YACvB,YAAY,OAAO,UAAU,IAAI;YACjC,UAAU,OAAO,QAAQ;YACzB,aAAa,OAAO,WAAW;QACjC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAEA,oCAAoC;AACpC,MAAM,gBAAgB;IACpB,gCAAgC;IAChC,cAAc;QACZ;YAAE,SAAS;YAAmC,MAAM;gBAAC;gBAAS;aAAQ;QAAC;QACvE;YAAE,SAAS;YAAkC,MAAM;gBAAC;gBAAO;aAAS;QAAC;QACrE;YAAE,SAAS;YAAiC,MAAM;gBAAC;gBAAO;aAAS;QAAC;QACpE;YAAE,SAAS;YAAkC,MAAM;gBAAC;aAAW;QAAC;QAChE;YAAE,SAAS;YAAgC,MAAM;gBAAC;gBAAQ;aAAQ;QAAC;QACnE;YAAE,SAAS;YAAgC,MAAM;gBAAC;gBAAS;aAAS;QAAC;QACrE;YAAE,SAAS;YAA8B,MAAM;gBAAC;gBAAU;aAAU;QAAC;QACrE;YAAE,SAAS;YAAoC,MAAM;gBAAC;gBAAa;aAAW;QAAC;QAC/E;YAAE,SAAS;YAA0C,MAAM;gBAAC;aAAuB;QAAC;QACpF;YAAE,SAAS;YAAoC,MAAM;gBAAC;gBAAgB;aAAS;QAAC;QAChF;YAAE,SAAS;YAAiC,MAAM;gBAAC;gBAAmB;aAAQ;QAAC;QAC/E;YAAE,SAAS;YAAwC,MAAM;gBAAC;gBAAkB;aAAS;QAAC;QACtF;YAAE,SAAS;YAAsC,MAAM;gBAAC;aAAQ;QAAC;QACjE;YAAE,SAAS;YAA8B,MAAM;gBAAC;gBAAQ;aAAO;QAAC;QAChE;YAAE,SAAS;YAAyB,MAAM;gBAAC;gBAAQ;aAAiB;QAAC;QACrE;YAAE,SAAS;YAA4B,MAAM;gBAAC;aAAS;QAAC;QACxD;YAAE,SAAS;YAAyC,MAAM;gBAAC;gBAAe;aAAO;QAAC;QAClF;YAAE,SAAS;YAA+B,MAAM;gBAAC;gBAAQ;aAAS;QAAC;QACnE;YAAE,SAAS;YAAsC,MAAM;gBAAC;gBAAU;aAAY;QAAC;QAC/E;YAAE,SAAS;YAAgC,MAAM;gBAAC;gBAAW;aAAY;QAAC;QAC1E;YAAE,SAAS;YAAsC,MAAM;gBAAC;gBAAS;aAAW;QAAC;QAC7E;YAAE,SAAS;YAA6B,MAAM;gBAAC;aAAe;QAAC;KAChE;IAED,qBAAqB;IACrB,kBAAkB;QAChB;YAAE,SAAS;YAAU,MAAM;gBAAC;aAAQ;QAAC;QACrC;YAAE,SAAS;YAAQ,MAAM;gBAAC;aAAM;QAAC;QACjC;YAAE,SAAS;YAAQ,MAAM;gBAAC;aAAM;QAAC;QACjC;YAAE,SAAS;YAAa,MAAM;gBAAC;aAAW;QAAC;QAC3C;YAAE,SAAS;YAAU,MAAM;gBAAC;aAAQ;QAAC;QACrC;YAAE,SAAS;YAAW,MAAM;gBAAC;aAAS;QAAC;QACvC;YAAE,SAAS;YAAY,MAAM;gBAAC;gBAAW;aAAY;QAAC;QACtD;YAAE,SAAS;YAAU,MAAM;gBAAC;aAAQ;QAAC;QACrC;YAAE,SAAS;YAAS,MAAM;gBAAC;aAAO;QAAC;QACnC;YAAE,SAAS;YAAS,MAAM;gBAAC;aAAO;QAAC;KACpC;IAED,gCAAgC;IAChC,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,kDAAkD;AAClD,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,eAAe,oBAAoB,IAAU,EAAE,KAAc;IAClE,IAAI;QACF,wBAAwB;QACxB,OAAO,MAAM,mBAAmB,MAAM;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,uCAAuC;QACpD,gCAAgC;QAChC,OAAO,iBAAiB,MAAM;IAChC;AACF;AAEA,2CAA2C;AAC3C,SAAS,iBAAiB,IAAU,EAAE,KAAc;IAClD,MAAM,OAAO,IAAI;IACjB,IAAI,aAAa;IAEjB,mBAAmB;IACnB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;IACtC,cAAc,gBAAgB,CAAC,OAAO,CAAC;YAAC,EAAE,OAAO,EAAE,MAAM,WAAW,EAAE;QACpE,IAAI,QAAQ,IAAI,CAAC,WAAW;YAC1B,YAAY,OAAO,CAAC,CAAA,MAAO,KAAK,GAAG,CAAC;YACpC,cAAc;QAChB;IACF;IAEA,4BAA4B;IAC5B,IAAI,OAAO;QACT,MAAM,aAAa,MAAM,WAAW;QACpC,cAAc,YAAY,CAAC,OAAO,CAAC;gBAAC,EAAE,OAAO,EAAE,MAAM,WAAW,EAAE;YAChE,IAAI,QAAQ,IAAI,CAAC,aAAa;gBAC5B,YAAY,OAAO,CAAC,CAAA,MAAO,KAAK,GAAG,CAAC;gBACpC,cAAc;YAChB;QACF;IACF;IAEA,uDAAuD;IACvD,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,IAAI,CAAA,kBAAA,4BAAA,MAAO,QAAQ,CAAC,YAAY,QAAQ,QAAO,SAAS,QAAQ,CAAC,YAAY,QAAQ,KAAK;QACxF,KAAK,GAAG,CAAC;QACT,cAAc;IAChB;IAEA,iDAAiD;IACjD,IAAI,KAAK,IAAI,KAAK,GAAG;QACnB,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,aAAa;IACf;IAEA,0DAA0D;IAC1D,MAAM,YAAY,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG;IAE5C,OAAO;QACL,MAAM;QACN,YAAY,KAAK,GAAG,CAAC,YAAY;QACjC,UAAU;IACZ;AACF;AAEO,SAAS,sBAAsB,YAAsB;IAC1D,MAAM,cAAc,IAAI;IACxB,MAAM,cAAc,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;IAE/D,8CAA8C;IAC9C,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,WAAW,IAAI,WAAW;QAEhC,6BAA6B;QAC7B,IAAI;YAAC;YAAO;YAAO;SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,KAAK;YAC5D;gBAAC;gBAAQ;gBAAO;aAAY,CAAC,OAAO,CAAC,CAAA;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YAC3C;QACF;QAEA,IAAI;YAAC;YAAS;YAAS;SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,KAAK;YAC7D;gBAAC;gBAAU;gBAAa;aAAO,CAAC,OAAO,CAAC,CAAA;gBACtC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YAC3C;QACF;QAEA,IAAI;YAAC;YAAU;SAAO,CAAC,IAAI,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,KAAK;YACtD;gBAAC;gBAAS;gBAAa;aAAU,CAAC,OAAO,CAAC,CAAA;gBACxC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YAC3C;QACF;QAEA,IAAI;YAAC;YAAQ;YAAU;SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,SAAS,QAAQ,CAAC,KAAK;YAC7D;gBAAC;gBAAa;gBAAU;aAAS,CAAC,OAAO,CAAC,CAAA;gBACxC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YAC3C;QACF;IACF;IAEA,sDAAsD;IACtD,IAAI,YAAY,IAAI,GAAG,GAAG;QACxB,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,CAAC,YAAY,GAAG,CAAC,QAAQ,YAAY,IAAI,GAAG,GAAG;gBACjD,YAAY,GAAG,CAAC;YAClB;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC,aAAa,KAAK,CAAC,GAAG;AAC1C;AAGO,SAAS;IACd,OAAO;QACL;QAAS;QAAa;QAAQ;QAAY;QAC1C;QAAO;QAAO;QAAU;QAAQ;KACjC;AACH", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/components/UploadMeme.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { uploadMeme } from '@/lib/api';\r\nimport { UploadMemeFormData } from '@/types/meme';\r\nimport { analyzeImageForTags } from '@/lib/autoTag';\r\nimport { Upload, Plus, X, AlertCircle, Loader2, Clipboard } from 'lucide-react';\r\n\r\ninterface UploadMemeProps {\r\n  onUploadSuccess?: () => void;\r\n  initialData?: {\r\n    title?: string;\r\n    text?: string;\r\n    url?: string;\r\n    imageUrl?: string;\r\n    hasImage?: boolean;\r\n  };\r\n}\r\n\r\nexport default function UploadMeme({ onUploadSuccess, initialData }: UploadMemeProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [dragActive, setDragActive] = useState(false);\r\n  const [showPasteHint, setShowPasteHint] = useState(false);\r\n  \r\n  const [formData, setFormData] = useState({\r\n    title: initialData?.title || '',\r\n    tags: '',\r\n    image: null as File | null,\r\n    preview: null as string | null,\r\n  });\r\n\r\n  // Update form data when initialData changes\r\n  useEffect(() => {\r\n    if (initialData?.title && !formData.title) {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        title: initialData.title || '',\r\n        tags: initialData.text ? `shared, ${initialData.text.slice(0, 20)}` : 'shared'\r\n      }));\r\n    }\r\n  }, [initialData, formData.title]);\r\n\r\n  // Global paste handler\r\n  useEffect(() => {\r\n    const handlePaste = (e: ClipboardEvent) => {\r\n      // Only handle paste if modal is open or we're not in an input field\r\n      const activeElement = document.activeElement;\r\n      const isInInput = activeElement instanceof HTMLInputElement || \r\n                       activeElement instanceof HTMLTextAreaElement ||\r\n                       activeElement?.getAttribute('contenteditable') === 'true';\r\n      \r\n      if (!isOpen && !isInInput) {\r\n        // Show paste hint for a few seconds\r\n        const clipboardItems = e.clipboardData?.items;\r\n        if (clipboardItems) {\r\n          for (let i = 0; i < clipboardItems.length; i++) {\r\n            if (clipboardItems[i].type.startsWith('image/')) {\r\n              setShowPasteHint(true);\r\n              setTimeout(() => setShowPasteHint(false), 3000);\r\n              break;\r\n            }\r\n          }\r\n        }\r\n        return;\r\n      }\r\n\r\n      if (isOpen && !isInInput) {\r\n        const clipboardItems = e.clipboardData?.items;\r\n        if (clipboardItems) {\r\n          for (let i = 0; i < clipboardItems.length; i++) {\r\n            if (clipboardItems[i].type.startsWith('image/')) {\r\n              e.preventDefault();\r\n              const blob = clipboardItems[i].getAsFile();\r\n              if (blob) {\r\n                handleFileSelect(blob);\r\n              }\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleGlobalDrop = (e: DragEvent) => {\r\n      // Prevent default browser behavior for file drops\r\n      if (e.dataTransfer?.types.includes('Files')) {\r\n        e.preventDefault();\r\n        \r\n        if (!isOpen) {\r\n          // Auto-open modal if user drops a file anywhere\r\n          const file = e.dataTransfer.files?.[0];\r\n          if (file && file.type.startsWith('image/')) {\r\n            setIsOpen(true);\r\n            setTimeout(() => handleFileSelect(file), 100);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleGlobalDragOver = (e: DragEvent) => {\r\n      if (e.dataTransfer?.types.includes('Files')) {\r\n        e.preventDefault();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('paste', handlePaste);\r\n    document.addEventListener('drop', handleGlobalDrop);\r\n    document.addEventListener('dragover', handleGlobalDragOver);\r\n\r\n    return () => {\r\n      document.removeEventListener('paste', handlePaste);\r\n      document.removeEventListener('drop', handleGlobalDrop);\r\n      document.removeEventListener('dragover', handleGlobalDragOver);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  // Show paste hint notification\r\n  const PasteHint = () => (\r\n    showPasteHint && (\r\n      <div className=\"fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Clipboard size={16} />\r\n          <span className=\"text-sm\">Image copied! Open upload to paste it</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  );\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setError(null);\r\n\r\n    if (!formData.image) {\r\n      setError('Please select an image');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      // Auto-generate tags if none provided\r\n      let finalTags = formData.tags.trim();\r\n      if (!finalTags) {\r\n        try {\r\n          const result = await analyzeImageForTags(formData.image, formData.title);\r\n          finalTags = result.tags.join(', ') || 'funny, meme';\r\n        } catch {\r\n          finalTags = 'funny, meme';\r\n        }\r\n      }\r\n\r\n      const uploadData: UploadMemeFormData = {\r\n        title: formData.title || undefined,\r\n        tags: finalTags,\r\n        image: formData.image,\r\n      };\r\n\r\n      const response = await uploadMeme(uploadData);\r\n\r\n      if (response.success) {\r\n        setFormData({ title: '', tags: '', image: null, preview: null });\r\n        setIsOpen(false);\r\n        onUploadSuccess?.();\r\n      } else {\r\n        setError(response.message || 'Upload failed');\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Upload failed');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFileSelect = (file: File) => {\r\n    if (!file.type.startsWith('image/')) {\r\n      setError('Please select an image file');\r\n      return;\r\n    }\r\n\r\n    if (file.size > 10 * 1024 * 1024) {\r\n      setError('File size must be less than 10MB');\r\n      return;\r\n    }\r\n\r\n    // Create preview\r\n    const reader = new FileReader();\r\n    reader.onload = (e) => {\r\n      setFormData(prev => ({ \r\n        ...prev, \r\n        image: file, \r\n        preview: e.target?.result as string,\r\n        title: prev.title || file.name.split('.')[0].replace(/[_-]/g, ' ')\r\n      }));\r\n    };\r\n    reader.readAsDataURL(file);\r\n    setError(null);\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) handleFileSelect(file);\r\n  };\r\n\r\n  const handleDrag = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(e.type === \"dragenter\" || e.type === \"dragover\");\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n    \r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) handleFileSelect(file);\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({ title: '', tags: '', image: null, preview: null });\r\n    setError(null);\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <>\r\n        <PasteHint />\r\n        <button\r\n          onClick={() => setIsOpen(true)}\r\n          className=\"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 group\"\r\n          title=\"Upload a meme (or paste/drop anywhere)\"\r\n        >\r\n          <Plus size={24} className=\"group-hover:rotate-90 transition-transform duration-200\" />\r\n        </button>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <PasteHint />\r\n      <div className=\"fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50\">\r\n        <div className=\"bg-white rounded-xl w-full max-w-lg shadow-2xl\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900\">Upload Meme</h2>\r\n          <button\r\n            onClick={() => setIsOpen(false)}\r\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6\">\r\n          {error && (\r\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 flex items-center gap-2\">\r\n              <AlertCircle size={16} />\r\n              <span className=\"text-sm\">{error}</span>\r\n            </div>\r\n          )}\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n            {/* File Upload Area */}\r\n            <div>\r\n              <div \r\n                className={`border-2 border-dashed rounded-lg p-8 text-center transition-all ${\r\n                  dragActive \r\n                    ? 'border-blue-400 bg-blue-50' \r\n                    : formData.image \r\n                      ? 'border-green-400 bg-green-50' \r\n                      : 'border-gray-300 hover:border-gray-400'\r\n                }`}\r\n                onDragEnter={handleDrag}\r\n                onDragLeave={handleDrag}\r\n                onDragOver={handleDrag}\r\n                onDrop={handleDrop}\r\n              >\r\n                <input\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  onChange={handleFileChange}\r\n                  className=\"hidden\"\r\n                  id=\"file-upload\"\r\n                />\r\n\r\n                {formData.preview ? (\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"relative w-32 h-32 mx-auto\">\r\n                      <Image \r\n                        src={formData.preview} \r\n                        alt=\"Preview\" \r\n                        fill\r\n                        className=\"rounded-lg object-cover\"\r\n                      />\r\n                    </div>\r\n                    <p className=\"text-sm text-green-700 font-medium\">{formData.image?.name}</p>\r\n                    <div className=\"flex gap-2 justify-center\">\r\n                      <label\r\n                        htmlFor=\"file-upload\"\r\n                        className=\"px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\"\r\n                      >\r\n                        Change\r\n                      </label>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={resetForm}\r\n                        className=\"px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\r\n                      >\r\n                        Remove\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-3\">\r\n                    <Upload size={40} className=\"mx-auto text-gray-400\" />\r\n                    <div>\r\n                      <p className=\"text-lg font-medium text-gray-700 mb-1\">\r\n                        Drop your meme here\r\n                      </p>\r\n                      <p className=\"text-sm text-gray-500 mb-2\">or click to browse</p>\r\n                      <div className=\"flex items-center justify-center gap-4 text-xs text-gray-400\">\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Upload size={12} />\r\n                          <span>Drag & Drop</span>\r\n                        </div>\r\n                        <div className=\"w-px h-4 bg-gray-300\"></div>\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Clipboard size={12} />\r\n                          <span>Ctrl+V to Paste</span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <label\r\n                      htmlFor=\"file-upload\"\r\n                      className=\"inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors font-medium\"\r\n                    >\r\n                      Choose File\r\n                    </label>\r\n                    <p className=\"text-xs text-gray-400\">PNG, JPG, GIF up to 10MB</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Form Fields - Only show when image is selected */}\r\n            {formData.image && (\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Title (optional)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.title}\r\n                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors\"\r\n                    placeholder=\"Give your meme a catchy title...\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Tags (optional)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={formData.tags}\r\n                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors\"\r\n                    placeholder=\"funny, relatable, viral...\"\r\n                  />\r\n                  <p className=\"text-xs text-gray-500 mt-1\">\r\n                    Separate with commas. Leave empty for auto-generated tags.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action Buttons */}\r\n            {formData.image && (\r\n              <div className=\"flex gap-3 pt-4\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setIsOpen(false)}\r\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\r\n                  disabled={loading}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2\"\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Loader2 size={16} className=\"animate-spin\" />\r\n                      Uploading...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Upload size={16} />\r\n                      Upload Meme\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            )}\r\n          </form>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAoBe,SAAS,WAAW,KAAiD;QAAjD,EAAE,eAAe,EAAE,WAAW,EAAmB,GAAjD;QAwRoC;;IAvRrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO,CAAA,wBAAA,kCAAA,YAAa,KAAK,KAAI;QAC7B,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAA,wBAAA,kCAAA,YAAa,KAAK,KAAI,CAAC,SAAS,KAAK,EAAE;gBACzC;4CAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,OAAO,YAAY,KAAK,IAAI;4BAC5B,MAAM,YAAY,IAAI,GAAG,AAAC,WAAwC,OAA9B,YAAY,IAAI,CAAC,KAAK,CAAC,GAAG,OAAQ;wBACxE,CAAC;;YACH;QACF;+BAAG;QAAC;QAAa,SAAS,KAAK;KAAC;IAEhC,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;oDAAc,CAAC;oBACnB,oEAAoE;oBACpE,MAAM,gBAAgB,SAAS,aAAa;oBAC5C,MAAM,YAAY,yBAAyB,oBAC1B,yBAAyB,uBACzB,CAAA,0BAAA,oCAAA,cAAe,YAAY,CAAC,wBAAuB;oBAEpE,IAAI,CAAC,UAAU,CAAC,WAAW;4BAEF;wBADvB,oCAAoC;wBACpC,MAAM,kBAAiB,mBAAA,EAAE,aAAa,cAAf,uCAAA,iBAAiB,KAAK;wBAC7C,IAAI,gBAAgB;4BAClB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gCAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW;oCAC/C,iBAAiB;oCACjB;4EAAW,IAAM,iBAAiB;2EAAQ;oCAC1C;gCACF;4BACF;wBACF;wBACA;oBACF;oBAEA,IAAI,UAAU,CAAC,WAAW;4BACD;wBAAvB,MAAM,kBAAiB,oBAAA,EAAE,aAAa,cAAf,wCAAA,kBAAiB,KAAK;wBAC7C,IAAI,gBAAgB;4BAClB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gCAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW;oCAC/C,EAAE,cAAc;oCAChB,MAAM,OAAO,cAAc,CAAC,EAAE,CAAC,SAAS;oCACxC,IAAI,MAAM;wCACR,iBAAiB;oCACnB;oCACA;gCACF;4BACF;wBACF;oBACF;gBACF;;YAEA,MAAM;yDAAmB,CAAC;wBAEpB;oBADJ,kDAAkD;oBAClD,KAAI,kBAAA,EAAE,YAAY,cAAd,sCAAA,gBAAgB,KAAK,CAAC,QAAQ,CAAC,UAAU;wBAC3C,EAAE,cAAc;wBAEhB,IAAI,CAAC,QAAQ;gCAEE;4BADb,gDAAgD;4BAChD,MAAM,QAAO,wBAAA,EAAE,YAAY,CAAC,KAAK,cAApB,4CAAA,qBAAsB,CAAC,EAAE;4BACtC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gCAC1C,UAAU;gCACV;6EAAW,IAAM,iBAAiB;4EAAO;4BAC3C;wBACF;oBACF;gBACF;;YAEA,MAAM;6DAAuB,CAAC;wBACxB;oBAAJ,KAAI,kBAAA,EAAE,YAAY,cAAd,sCAAA,gBAAgB,KAAK,CAAC,QAAQ,CAAC,UAAU;wBAC3C,EAAE,cAAc;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,SAAS;YACnC,SAAS,gBAAgB,CAAC,QAAQ;YAClC,SAAS,gBAAgB,CAAC,YAAY;YAEtC;wCAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,QAAQ;oBACrC,SAAS,mBAAmB,CAAC,YAAY;gBAC3C;;QACF;+BAAG;QAAC;KAAO;IAEX,+BAA+B;IAC/B,MAAM,YAAY,IAChB,+BACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+MAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;kCACjB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAMlC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,SAAS;YACT;QACF;QAEA,WAAW;QAEX,IAAI;YACF,sCAAsC;YACtC,IAAI,YAAY,SAAS,IAAI,CAAC,IAAI;YAClC,IAAI,CAAC,WAAW;gBACd,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,KAAK,EAAE,SAAS,KAAK;oBACvE,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;gBACxC,EAAE,UAAM;oBACN,YAAY;gBACd;YACF;YAEA,MAAM,aAAiC;gBACrC,OAAO,SAAS,KAAK,IAAI;gBACzB,MAAM;gBACN,OAAO,SAAS,KAAK;YACvB;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;YAElC,IAAI,SAAS,OAAO,EAAE;gBACpB,YAAY;oBAAE,OAAO;oBAAI,MAAM;oBAAI,OAAO;oBAAM,SAAS;gBAAK;gBAC9D,UAAU;gBACV,4BAAA,sCAAA;YACF,OAAO;gBACL,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,SAAS;YACT;QACF;QAEA,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,YAAY,CAAA;oBAGD;uBAHU;oBACnB,GAAG,IAAI;oBACP,OAAO;oBACP,OAAO,GAAE,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;oBACzB,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS;gBAChE;;QACF;QACA,OAAO,aAAa,CAAC;QACrB,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;YACX;QAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QAChC,IAAI,MAAM,iBAAiB;IAC7B;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK;IACrD;IAEA,MAAM,aAAa,CAAC;YAKL;QAJb,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,QAAO,wBAAA,EAAE,YAAY,CAAC,KAAK,cAApB,4CAAA,qBAAsB,CAAC,EAAE;QACtC,IAAI,MAAM,iBAAiB;IAC7B;IAEA,MAAM,YAAY;QAChB,YAAY;YAAE,OAAO;YAAI,MAAM;YAAI,OAAO;YAAM,SAAS;QAAK;QAC9D,SAAS;IACX;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE;;8BACE,6LAAC;;;;;8BACD,6LAAC;oBACC,SAAS,IAAM,UAAU;oBACzB,WAAU;oBACV,OAAM;8BAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;;;IAIlC;IAEA,qBACE;;0BACE,6LAAC;;;;;0BACD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKb,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;8CAI/B,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,6LAAC;sDACC,cAAA,6LAAC;gDACC,WAAW,AAAC,oEAMX,OALC,aACI,+BACA,SAAS,KAAK,GACZ,iCACA;gDAER,aAAa;gDACb,aAAa;gDACb,YAAY;gDACZ,QAAQ;;kEAER,6LAAC;wDACC,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;wDACV,IAAG;;;;;;oDAGJ,SAAS,OAAO,iBACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,SAAS,OAAO;oEACrB,KAAI;oEACJ,IAAI;oEACJ,WAAU;;;;;;;;;;;0EAGd,6LAAC;gEAAE,WAAU;2EAAsC,kBAAA,SAAS,KAAK,cAAd,sCAAA,gBAAgB,IAAI;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAQ;wEACR,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,MAAK;wEACL,SAAS;wEACT,WAAU;kFACX;;;;;;;;;;;;;;;;;6EAML,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yMAAA,CAAA,SAAM;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC5B,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFAGtD,6LAAC;wEAAE,WAAU;kFAA6B;;;;;;kFAC1C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,yMAAA,CAAA,SAAM;wFAAC,MAAM;;;;;;kGACd,6LAAC;kGAAK;;;;;;;;;;;;0FAER,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,+MAAA,CAAA,YAAS;wFAAC,MAAM;;;;;;kGACjB,6LAAC;kGAAK;;;;;;;;;;;;;;;;;;;;;;;;0EAIZ,6LAAC;gEACC,SAAQ;gEACR,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;wCAO5C,SAAS,KAAK,kBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACxE,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;wCAQ/C,SAAS,KAAK,kBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,UAAU;oDACzB,WAAU;oDACV,UAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,wBACC;;0EACE,6LAAC,oNAAA,CAAA,UAAO;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAiB;;qFAIhD;;0EACE,6LAAC,yMAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa1C;GA/YwB;KAAA", "debugId": null}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/components/SearchFilters.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { SearchQuery } from '@/types/meme';\r\nimport { Search, X, Filter } from 'lucide-react';\r\n\r\ninterface SearchFiltersProps {\r\n  onSearch: (query: SearchQuery) => void;\r\n  onClear: () => void;\r\n}\r\n\r\nexport default function SearchFilters({ onSearch, onClear }: SearchFiltersProps) {\r\n  const [searchValue, setSearchValue] = useState('');\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [limit, setLimit] = useState<number | undefined>(undefined);\r\n\r\n  // Auto-search with debounce\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      const query: SearchQuery = {};\r\n      \r\n      if (searchValue.trim()) {\r\n        query.q = searchValue.trim();\r\n      }\r\n      \r\n      if (limit) {\r\n        query.limit = limit;\r\n      }\r\n\r\n      // Only search if there's actually something to search for\r\n      if (searchValue.trim() || limit) {\r\n        onSearch(query);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [searchValue, limit, onSearch]);\r\n\r\n  const handleSearch = () => {\r\n    const query: SearchQuery = {};\r\n    \r\n    if (searchValue.trim()) {\r\n      query.q = searchValue.trim();\r\n    }\r\n    \r\n    if (limit) {\r\n      query.limit = limit;\r\n    }\r\n\r\n    onSearch(query);\r\n  };\r\n\r\n  const handleClear = () => {\r\n    setSearchValue('');\r\n    setLimit(undefined);\r\n    setIsOpen(false);\r\n    onClear();\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter') {\r\n      handleSearch();\r\n    }\r\n  };\r\n\r\n  const hasActiveSearch = searchValue.trim() || limit;\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Mobile-first: Search input with filter toggle */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {/* Main Search Input */}\r\n        <div className=\"relative flex-1 min-w-0\">\r\n          <Search size={16} className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n          <input\r\n            type=\"text\"\r\n            value={searchValue}\r\n            onChange={(e) => setSearchValue(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            className=\"w-full pl-9 pr-9 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-gray-900 placeholder-gray-500\"\r\n            placeholder=\"Search memes...\"\r\n          />\r\n          {searchValue && (\r\n            <button\r\n              onClick={() => setSearchValue('')}\r\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\"\r\n            >\r\n              <X size={14} />\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Filter Toggle (Mobile) / Limit Selector (Desktop) */}\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Desktop: Always show limit selector */}\r\n          <select\r\n            value={limit || ''}\r\n            onChange={(e) => setLimit(e.target.value ? parseInt(e.target.value) : undefined)}\r\n            className=\"hidden sm:block px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white text-gray-900\"\r\n          >\r\n            <option value=\"\">All</option>\r\n            <option value=\"10\">10</option>\r\n            <option value=\"20\">20</option>\r\n            <option value=\"50\">50</option>\r\n          </select>\r\n\r\n          {/* Mobile: Filter toggle button */}\r\n          <button\r\n            onClick={() => setIsOpen(!isOpen)}\r\n            className=\"sm:hidden p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\r\n          >\r\n            <Filter size={16} className=\"text-gray-600\" />\r\n          </button>\r\n\r\n          {/* Clear Button */}\r\n          {hasActiveSearch && (\r\n            <button\r\n              onClick={handleClear}\r\n              className=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\r\n            >\r\n              Clear\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile: Dropdown filter panel */}\r\n      {isOpen && (\r\n        <div className=\"sm:hidden absolute top-full left-0 right-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\r\n          <div className=\"space-y-3\">\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Results limit\r\n              </label>\r\n              <select\r\n                value={limit || ''}\r\n                onChange={(e) => setLimit(e.target.value ? parseInt(e.target.value) : undefined)}\r\n                className=\"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900\"\r\n              >\r\n                <option value=\"\">Show all results</option>\r\n                <option value=\"10\">Show 10 results</option>\r\n                <option value=\"20\">Show 20 results</option>\r\n                <option value=\"50\">Show 50 results</option>\r\n              </select>\r\n            </div>\r\n            \r\n            <div className=\"flex gap-2 pt-2\">\r\n              <button\r\n                onClick={handleSearch}\r\n                className=\"flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors\"\r\n              >\r\n                Apply\r\n              </button>\r\n              <button\r\n                onClick={() => setIsOpen(false)}\r\n                className=\"px-3 py-2 text-gray-600 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\r\n              >\r\n                Close\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Search hint */}\r\n      {searchValue && (\r\n        <div className=\"mt-2 text-xs text-gray-500\">\r\n          💡 Try: &ldquo;funny cats&rdquo;, &ldquo;#meme&rdquo;, &ldquo;viral content&rdquo;\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;;;AAJA;;;AAWe,SAAS,cAAc,KAAyC;QAAzC,EAAE,QAAQ,EAAE,OAAO,EAAsB,GAAzC;;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEvD,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ;iDAAW;oBACvB,MAAM,QAAqB,CAAC;oBAE5B,IAAI,YAAY,IAAI,IAAI;wBACtB,MAAM,CAAC,GAAG,YAAY,IAAI;oBAC5B;oBAEA,IAAI,OAAO;wBACT,MAAM,KAAK,GAAG;oBAChB;oBAEA,0DAA0D;oBAC1D,IAAI,YAAY,IAAI,MAAM,OAAO;wBAC/B,SAAS;oBACX;gBACF;gDAAG;YAEH;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;QAAa;QAAO;KAAS;IAEjC,MAAM,eAAe;QACnB,MAAM,QAAqB,CAAC;QAE5B,IAAI,YAAY,IAAI,IAAI;YACtB,MAAM,CAAC,GAAG,YAAY,IAAI;QAC5B;QAEA,IAAI,OAAO;YACT,MAAM,KAAK,GAAG;QAChB;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,SAAS;QACT,UAAU;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,kBAAkB,YAAY,IAAI,MAAM;IAE9C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC5B,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,YAAY;gCACZ,WAAU;gCACV,aAAY;;;;;;4BAEb,6BACC,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAMf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,OAAO,SAAS;gCAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gCACtE,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;0CAIrB,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;4BAI7B,iCACC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAQN,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,SAAS;oCAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;oCACtE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;;;;;;;;;;;;;sCAIvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,6BACC,6LAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAMpD;GAjKwB;KAAA", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/components/LoadingSkeleton.tsx"], "sourcesContent": ["export default function LoadingSkeleton() {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6\">\r\n      {Array.from({ length: 10 }).map((_, index) => (\r\n        <div key={index} className=\"bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100\">\r\n          {/* Image skeleton */}\r\n          <div className=\"w-full h-64 bg-gray-200 skeleton\" />\r\n          \r\n          {/* Content skeleton */}\r\n          <div className=\"p-5 space-y-3\">\r\n            {/* Title skeleton */}\r\n            <div className=\"h-5 bg-gray-200 rounded skeleton\" />\r\n            <div className=\"h-4 bg-gray-200 rounded skeleton w-3/4\" />\r\n            \r\n            {/* Tags skeleton */}\r\n            <div className=\"flex gap-2\">\r\n              <div className=\"h-6 w-12 bg-gray-200 rounded skeleton\" />\r\n              <div className=\"h-6 w-16 bg-gray-200 rounded skeleton\" />\r\n            </div>\r\n            \r\n            {/* Date skeleton */}\r\n            <div className=\"h-4 bg-gray-200 rounded skeleton w-1/2\" />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,6LAAC;gBAAgB,WAAU;;kCAEzB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;eAjBT;;;;;;;;;;AAuBlB;KA3BwB", "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/memedb/meme-f/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Meme, SearchQuery } from '@/types/meme';\r\nimport { getMemes, searchMemes, deleteMeme, healthCheck } from '@/lib/api';\r\nimport MemeCard from '@/components/MemeCard';\r\nimport UploadMeme from '@/components/UploadMeme';\r\nimport SearchFilters from '@/components/SearchFilters';\r\nimport LoadingSkeleton from '@/components/LoadingSkeleton';\r\nimport { RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';\r\n\r\nfunction Home() {\r\n  const [memes, setMemes] = useState<Meme[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');\r\n  const [currentQuery, setCurrentQuery] = useState<SearchQuery | null>(null);\r\n\r\n  // Check backend health\r\n  useEffect(() => {\r\n    const checkBackend = async () => {\r\n      try {\r\n        await healthCheck();\r\n        setBackendStatus('online');\r\n      } catch {\r\n        setBackendStatus('offline');\r\n      }\r\n    };\r\n    checkBackend();\r\n  }, []);\r\n\r\n  // Load memes\r\n  const loadMemes = async (query?: SearchQuery) => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = query && (query.q || query.tag || query.title || query.limit)\r\n        ? await searchMemes(query)\r\n        : await getMemes();\r\n\r\n      if (response.success && response.data) {\r\n        setMemes(response.data);\r\n        setCurrentQuery(query || null);\r\n      } else {\r\n        setError(response.message || 'Failed to load memes');\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load memes');\r\n      setBackendStatus('offline');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Initial load\r\n  useEffect(() => {\r\n    loadMemes();\r\n  }, []);\r\n\r\n  // Handle search\r\n  const handleSearch = (query: SearchQuery) => {\r\n    loadMemes(query);\r\n  };\r\n\r\n  // Handle clear search\r\n  const handleClearSearch = () => {\r\n    setCurrentQuery(null);\r\n    loadMemes();\r\n  };\r\n\r\n  // Handle delete\r\n  const handleDelete = async (id: string) => {\r\n    if (!confirm('Are you sure you want to delete this meme?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await deleteMeme(id);\r\n      if (response.success) {\r\n        // Reload memes after deletion\r\n        loadMemes(currentQuery || undefined);\r\n      } else {\r\n        alert('Failed to delete meme: ' + response.message);\r\n      }\r\n    } catch (err) {\r\n      alert('Failed to delete meme: ' + (err instanceof Error ? err.message : 'Unknown error'));\r\n    }\r\n  };\r\n\r\n  // Handle upload success\r\n  const handleUploadSuccess = () => {\r\n    loadMemes(currentQuery || undefined);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Simplified Header */}\r\n      <header className=\"bg-white border-b border-gray-200 sticky top-0 z-40\">\r\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\r\n          {/* Simple Header Layout */}\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* Logo & Title */}\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">\r\n                MemeDB\r\n              </h1>\r\n              <p className=\"text-sm text-gray-600\">Public meme collection</p>\r\n            </div>\r\n\r\n            {/* Header Actions */}\r\n            <div className=\"flex items-center gap-3\">\r\n              {/* Bookmarklet Link */}\r\n              <a\r\n                href=\"/help\"\r\n                className=\"hidden md:block px-3 py-1.5 text-xs bg-green-50 text-green-700 hover:bg-green-100 rounded-lg transition-colors font-medium\"\r\n                title=\"Learn how to save memes from anywhere\"\r\n              >\r\n                ❓ Help\r\n              </a>\r\n              <a\r\n                href=\"/save-memes.html\"\r\n                target=\"_blank\"\r\n                className=\"hidden lg:block px-3 py-1.5 text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 rounded-lg transition-colors font-medium\"\r\n                title=\"Learn how to save memes from any website\"\r\n              >\r\n                💾 Save Memes\r\n              </a>\r\n\r\n              {/* Status Indicator */}\r\n              <div className=\"hidden sm:block\">\r\n                {backendStatus === 'online' && (\r\n                  <div className=\"flex items-center gap-1.5 text-green-600\">\r\n                    <CheckCircle size={16} />\r\n                    <span className=\"text-sm font-medium\">Online</span>\r\n                  </div>\r\n                )}\r\n                {backendStatus === 'offline' && (\r\n                  <div className=\"flex items-center gap-1.5 text-red-600\">\r\n                    <AlertCircle size={16} />\r\n                    <span className=\"text-sm font-medium\">Offline</span>\r\n                  </div>\r\n                )}\r\n                {backendStatus === 'checking' && (\r\n                  <div className=\"flex items-center gap-1.5 text-gray-600\">\r\n                    <RefreshCw size={16} className=\"animate-spin\" />\r\n                    <span className=\"text-sm font-medium\">Connecting</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Search & Refresh */}\r\n              <SearchFilters onSearch={handleSearch} onClear={handleClearSearch} />\r\n              <button\r\n                onClick={() => loadMemes(currentQuery || undefined)}\r\n                className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\"\r\n                disabled={loading}\r\n                title=\"Refresh\"\r\n              >\r\n                <RefreshCw size={18} className={loading ? 'animate-spin' : ''} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Search Results Info */}\r\n          {currentQuery && (\r\n            <div className=\"mt-3 text-sm text-gray-600\">\r\n              <span>Showing results for: </span>\r\n              {currentQuery.title && <span className=\"font-medium\">&ldquo;{currentQuery.title}&rdquo;</span>}\r\n              {currentQuery.tag && <span className=\"font-medium\">#{currentQuery.tag}</span>}\r\n              {currentQuery.q && <span className=\"font-medium\">&ldquo;{currentQuery.q}&rdquo;</span>}\r\n              <button\r\n                onClick={handleClearSearch}\r\n                className=\"ml-2 text-blue-600 hover:text-blue-800 underline\"\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-6xl mx-auto px-4 py-8\">\r\n        {/* Error State */}\r\n        {error && (\r\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n            <div className=\"flex items-center gap-2 text-red-800\">\r\n              <AlertCircle size={20} />\r\n              <div>\r\n                <h3 className=\"font-medium\">Error</h3>\r\n                <p className=\"text-sm\">{error}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {loading && <LoadingSkeleton />}\r\n\r\n        {/* Empty State */}\r\n        {!loading && !error && memes.length === 0 && (\r\n          <div className=\"text-center py-16\">\r\n            <div className=\"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\r\n              <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n              </svg>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n              {currentQuery ? 'No memes found' : 'No memes yet'}\r\n            </h3>\r\n            <p className=\"text-gray-600 mb-4\">\r\n              {currentQuery \r\n                ? 'Try adjusting your search or browse all memes' \r\n                : 'Be the first to upload a meme to the collection!'\r\n              }\r\n            </p>\r\n            {currentQuery && (\r\n              <button\r\n                onClick={handleClearSearch}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n              >\r\n                Show All Memes\r\n              </button>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Memes Grid */}\r\n        {!loading && !error && memes.length > 0 && (\r\n          <div className=\"space-y-6\">\r\n            {/* Results Count */}\r\n            <div className=\"text-sm text-gray-600\">\r\n              {memes.length} meme{memes.length !== 1 ? 's' : ''} found\r\n            </div>\r\n\r\n            {/* Grid */}\r\n            <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4\">\r\n              {memes.map(meme => (\r\n                <MemeCard\r\n                  key={meme.id}\r\n                  meme={meme}\r\n                  onDelete={handleDelete}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </main>\r\n\r\n      {/* Upload FAB */}\r\n      <UploadMeme onUploadSuccess={handleUploadSuccess} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AATA;;;;;;;;AAWA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACtF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAErE,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;+CAAe;oBACnB,IAAI;wBACF,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;wBAChB,iBAAiB;oBACnB,EAAE,UAAM;wBACN,iBAAiB;oBACnB;gBACF;;YACA;QACF;yBAAG,EAAE;IAEL,aAAa;IACb,MAAM,YAAY,OAAO;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IACzE,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,SAClB,MAAM,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD;YAEjB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS,SAAS,IAAI;gBACtB,gBAAgB,SAAS;YAC3B,OAAO;gBACL,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,iBAAiB;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,UAAU;IACZ;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,gBAAgB;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,+CAA+C;YAC1D;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;YAClC,IAAI,SAAS,OAAO,EAAE;gBACpB,8BAA8B;gBAC9B,UAAU,gBAAgB;YAC5B,OAAO;gBACL,MAAM,4BAA4B,SAAS,OAAO;YACpD;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,4BAA4B,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,eAAe;QACzF;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,UAAU,gBAAgB;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,OAAM;sDACP;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,OAAM;sDACP;;;;;;sDAKD,6LAAC;4CAAI,WAAU;;gDACZ,kBAAkB,0BACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;gDAGzC,kBAAkB,2BACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;gDAGzC,kBAAkB,4BACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mNAAA,CAAA,YAAS;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC/B,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;;;;;;;sDAM5C,6LAAC,sIAAA,CAAA,UAAa;4CAAC,UAAU;4CAAc,SAAS;;;;;;sDAChD,6LAAC;4CACC,SAAS,IAAM,UAAU,gBAAgB;4CACzC,WAAU;4CACV,UAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,MAAM;gDAAI,WAAW,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;wBAMhE,8BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;gCACL,aAAa,KAAK,kBAAI,6LAAC;oCAAK,WAAU;;wCAAc;wCAAQ,aAAa,KAAK;wCAAC;;;;;;;gCAC/E,aAAa,GAAG,kBAAI,6LAAC;oCAAK,WAAU;;wCAAc;wCAAE,aAAa,GAAG;;;;;;;gCACpE,aAAa,CAAC,kBAAI,6LAAC;oCAAK,WAAU;;wCAAc;wCAAQ,aAAa,CAAC;wCAAC;;;;;;;8CACxE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;oBAEb,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;8CACnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAE,WAAU;sDAAW;;;;;;;;;;;;;;;;;;;;;;;oBAO/B,yBAAW,6LAAC,wIAAA,CAAA,UAAe;;;;;oBAG3B,CAAC,WAAW,CAAC,SAAS,MAAM,MAAM,KAAK,mBACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;0CACX,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCAAE,WAAU;0CACV,eACG,kDACA;;;;;;4BAGL,8BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;oBAQN,CAAC,WAAW,CAAC,SAAS,MAAM,MAAM,GAAG,mBACpC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,MAAM;oCAAC;oCAAM,MAAM,MAAM,KAAK,IAAI,MAAM;oCAAG;;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,iIAAA,CAAA,UAAQ;wCAEP,MAAM;wCACN,UAAU;uCAFL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAWxB,6LAAC,mIAAA,CAAA,UAAU;gBAAC,iBAAiB;;;;;;;;;;;;AAGnC;GAnPS;KAAA;uCAqPM", "debugId": null}}]}