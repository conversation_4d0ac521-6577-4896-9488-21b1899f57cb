# 🎨 Extension Icons Needed

The browser extension needs the following icon files:

## Required Sizes:
- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows)
- `icon48.png` - 48x48 pixels (extension management)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Design Guidelines:
- Use the MemeDB theme colors: #667eea to #764ba2 gradient
- Include the 🎭 emoji or similar meme-related icon
- Keep it simple and recognizable at small sizes
- Use PNG format with transparency
- Follow browser extension icon guidelines

## Tools to Create Icons:
- **Figma** - Free design tool
- **Canva** - Easy icon creator
- **GIMP** - Free image editor
- **Adobe Illustrator** - Professional tool

## Quick Solution:
You can use online favicon generators or icon creators to quickly generate all sizes from a single design.

## Temporary Workaround:
For testing, you can use any 16x16, 32x32, 48x48, and 128x128 PNG files and rename them appropriately.
