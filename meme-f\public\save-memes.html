<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How to Save Memes - MemeDB</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 40px;
            font-size: 1.2rem;
        }
        
        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .method-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .method-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .method-card.recommended::before {
            content: "RECOMMENDED";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .method-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .method-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3748;
        }
        
        .method-description {
            color: #718096;
            margin-bottom: 20px;
        }
        
        .method-steps {
            text-align: left;
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .method-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .method-steps li {
            margin: 8px 0;
            color: #4a5568;
        }
        
        .install-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 15px 0;
            transition: transform 0.2s;
        }
        
        .install-button:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .url-import {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .url-input {
            width: 100%;
            max-width: 500px;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
        }
        
        .url-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .import-button {
            background: #38b2ac;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
            transition: background 0.2s;
        }
        
        .import-button:hover {
            background: #319795;
        }
        
        .comparison-section {
            margin: 40px 0;
            padding: 30px;
            background: #f7fafc;
            border-radius: 12px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .comparison-table th {
            background: #f7fafc;
            font-weight: bold;
            color: #2d3748;
        }
        
        .check {
            color: #10b981;
            font-weight: bold;
        }
        
        .cross {
            color: #ef4444;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .methods-grid {
                grid-template-columns: 1fr;
            }
            
            .url-input {
                margin-bottom: 15px;
            }
            
            .import-button {
                margin-left: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 How to Save Memes</h1>
        <p class="subtitle">Modern, easy ways to save images to your MemeDB from anywhere!</p>
        
        <!-- Quick URL Import -->
        <div class="url-import">
            <h3>🔗 Quick Import from URL</h3>
            <p>Paste an image URL to save it instantly</p>
            <div>
                <input type="url" class="url-input" placeholder="https://example.com/image.jpg" id="imageUrl">
                <button class="import-button" onclick="importFromUrl()">Import Image</button>
            </div>
        </div>
        
        <!-- Methods Grid -->
        <div class="methods-grid">
            <!-- PWA Share Target -->
            <div class="method-card recommended">
                <div class="method-icon">📱</div>
                <div class="method-title">Mobile Share Menu</div>
                <div class="method-description">Best for mobile users - appears in your phone's share menu!</div>
                <a href="/" class="install-button">Install MemeDB App</a>
                <div class="method-steps">
                    <ol>
                        <li>Install MemeDB as an app (one-time setup)</li>
                        <li>Find any image in Photos, Reddit, Twitter, etc.</li>
                        <li>Tap "Share" → Select "MemeDB"</li>
                        <li>Add title/tags and save!</li>
                    </ol>
                </div>
            </div>
            
            <!-- Drag & Drop -->
            <div class="method-card">
                <div class="method-icon">🖱️</div>
                <div class="method-title">Drag & Drop</div>
                <div class="method-description">Perfect for desktop - drag images directly to MemeDB</div>
                <a href="/upload" class="install-button">Try Drag & Drop</a>
                <div class="method-steps">
                    <ol>
                        <li>Open MemeDB in a browser tab</li>
                        <li>Drag any image from anywhere</li>
                        <li>Drop it on the upload page</li>
                        <li>Upload dialog opens automatically</li>
                    </ol>
                </div>
            </div>
            
            <!-- Copy & Paste -->
            <div class="method-card">
                <div class="method-icon">📋</div>
                <div class="method-title">Copy & Paste</div>
                <div class="method-description">Quick saves with keyboard shortcuts</div>
                <a href="/upload" class="install-button">Try Copy & Paste</a>
                <div class="method-steps">
                    <ol>
                        <li>Copy image (Ctrl+C) from anywhere</li>
                        <li>Go to MemeDB upload page</li>
                        <li>Paste (Ctrl+V)</li>
                        <li>Upload dialog opens instantly</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <!-- Comparison Section -->
        <div class="comparison-section">
            <h2>📊 Method Comparison</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Mobile</th>
                        <th>Desktop</th>
                        <th>Setup Required</th>
                        <th>Ease of Use</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Mobile Share Menu</strong></td>
                        <td class="check">✅ Perfect</td>
                        <td class="cross">❌ No</td>
                        <td>Install PWA once</td>
                        <td class="check">✅ Very Easy</td>
                    </tr>
                    <tr>
                        <td><strong>Drag & Drop</strong></td>
                        <td class="cross">❌ Limited</td>
                        <td class="check">✅ Perfect</td>
                        <td>None</td>
                        <td class="check">✅ Very Easy</td>
                    </tr>
                    <tr>
                        <td><strong>Copy & Paste</strong></td>
                        <td class="check">✅ Good</td>
                        <td class="check">✅ Perfect</td>
                        <td>None</td>
                        <td class="check">✅ Easy</td>
                    </tr>
                    <tr>
                        <td><strong>URL Import</strong></td>
                        <td class="check">✅ Good</td>
                        <td class="check">✅ Good</td>
                        <td>None</td>
                        <td>⚡ Fast</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p style="text-align: center; margin-top: 40px;">
            <a href="/">← Back to MemeDB</a>
        </p>
    </div>

    <script>
        function importFromUrl() {
            const url = document.getElementById('imageUrl').value;
            if (url) {
                const uploadUrl = new URL('/upload', window.location.origin);
                uploadUrl.searchParams.set('imageUrl', url);
                window.location.href = uploadUrl.toString();
            } else {
                alert('Please enter an image URL');
            }
        }
        
        // Allow Enter key to import
        document.getElementById('imageUrl').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                importFromUrl();
            }
        });
    </script>
</body>
</html>
