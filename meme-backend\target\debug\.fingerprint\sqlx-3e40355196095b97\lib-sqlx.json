{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"default\", \"macros\", \"migrate\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"all\", \"all-databases\", \"all-types\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"decimal\", \"default\", \"git2\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mssql\", \"mysql\", \"offline\", \"postgres\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"time\", \"tls\", \"uuid\"]", "target": 3003836824758849296, "profile": 2241668132362809309, "path": 10765202605026215237, "deps": [[3263945008814830432, "sqlx_core", false, 16301510095099981535], [5928339019345152323, "sqlx_macros", false, 11470696616098406199]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-3e40355196095b97\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}