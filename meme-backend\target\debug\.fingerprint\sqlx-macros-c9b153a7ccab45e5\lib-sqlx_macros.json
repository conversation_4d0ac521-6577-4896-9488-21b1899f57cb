{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"migrate\", \"runtime-tokio-rustls\", \"sha2\", \"sqlite\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"decimal\", \"default\", \"hex\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mssql\", \"mysql\", \"offline\", \"postgres\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"serde\", \"serde_json\", \"sha2\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 2225463790103693989, "path": 17656518629099990634, "deps": [[2713742371683562785, "syn", false, 12747651474894396788], [3060637413840920116, "proc_macro2", false, 17515301829983154744], [3150220818285335163, "url", false, 18246970257470212370], [3263945008814830432, "sqlx_core", false, 3344063915368355483], [3405707034081185165, "dotenvy", false, 11776773280536702065], [3722963349756955755, "once_cell", false, 10849160098691136940], [8045585743974080694, "heck", false, 1057231227269787280], [9857275760291862238, "sha2", false, 2256126064678007473], [12170264697963848012, "either", false, 7810909085640143695], [17055994635158723026, "sqlx_rt", false, 1782016545619217520], [17990358020177143287, "quote", false, 4171954330046512192]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-c9b153a7ccab45e5\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}