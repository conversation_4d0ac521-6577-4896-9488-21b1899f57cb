// Options page script for MemeDB browser extension

document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('settingsForm');
  const statusDiv = document.getElementById('status');
  
  // Default settings
  const defaultSettings = {
    memedbUrl: 'http://localhost:3000',
    apiUrl: 'http://127.0.0.1:3001',
    openAfterSave: false,
    showNotifications: true,
    autoTags: true,
    defaultTags: 'browser-extension, saved'
  };
  
  // Load saved settings
  loadSettings();
  
  // Save settings when form is submitted
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    saveSettings();
  });
  
  async function loadSettings() {
    try {
      const settings = await chrome.storage.sync.get(defaultSettings);
      
      document.getElementById('memedbUrl').value = settings.memedbUrl;
      document.getElementById('apiUrl').value = settings.apiUrl;
      document.getElementById('openAfterSave').checked = settings.openAfterSave;
      document.getElementById('showNotifications').checked = settings.showNotifications;
      document.getElementById('autoTags').checked = settings.autoTags;
      document.getElementById('defaultTags').value = settings.defaultTags;
      
    } catch (error) {
      console.error('Error loading settings:', error);
      showStatus('❌ Failed to load settings', 'error');
    }
  }
  
  async function saveSettings() {
    try {
      const settings = {
        memedbUrl: document.getElementById('memedbUrl').value || defaultSettings.memedbUrl,
        apiUrl: document.getElementById('apiUrl').value || defaultSettings.apiUrl,
        openAfterSave: document.getElementById('openAfterSave').checked,
        showNotifications: document.getElementById('showNotifications').checked,
        autoTags: document.getElementById('autoTags').checked,
        defaultTags: document.getElementById('defaultTags').value || defaultSettings.defaultTags
      };
      
      await chrome.storage.sync.set(settings);
      showStatus('✅ Settings saved successfully!', 'success');
      
    } catch (error) {
      console.error('Error saving settings:', error);
      showStatus('❌ Failed to save settings', 'error');
    }
  }
  
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 3000);
  }
});
