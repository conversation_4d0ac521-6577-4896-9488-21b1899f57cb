{"rustc": 1842507548689473721, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 11751683919201195556, "deps": [[966925859616469517, "ahash", false, 12071517802198520842], [9150530836556604396, "allocator_api2", false, 14561773269873342361]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-6366895d298d8323\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}