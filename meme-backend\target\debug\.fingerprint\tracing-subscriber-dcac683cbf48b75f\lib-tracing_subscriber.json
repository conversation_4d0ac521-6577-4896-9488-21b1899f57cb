{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 16653591586134597239, "deps": [[1009387600818341822, "matchers", false, 11637245235565893541], [1017461770342116999, "sharded_slab", false, 2119159987155228462], [1359731229228270592, "thread_local", false, 13303519580497814013], [3424551429995674438, "tracing_core", false, 6126115333320033788], [3666196340704888985, "smallvec", false, 9689857978218393162], [3722963349756955755, "once_cell", false, 6438557561859163693], [8606274917505247608, "tracing", false, 17408597844338799234], [8614575489689151157, "nu_ansi_term", false, 561757035697366845], [9451456094439810778, "regex", false, 16347653007293040149], [10806489435541507125, "tracing_log", false, 2306248440594412810]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-dcac683cbf48b75f\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}