<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>MemeDB Saver - Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f8fafc;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        h1 {
            margin: 0;
            color: #2d3748;
        }
        
        .subtitle {
            color: #718096;
            margin: 5px 0 0 0;
        }
        
        .setting-group {
            margin: 25px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .setting-group h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
            font-size: 1.1rem;
        }
        
        .setting-group p {
            margin: 0 0 15px 0;
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }
        
        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        input[type="url"]:focus, input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin: 10px 0;
        }
        
        .save-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
            margin-top: 20px;
        }
        
        .save-button:hover {
            transform: translateY(-1px);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .help-text {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎭</div>
            <h1>MemeDB Saver Settings</h1>
            <p class="subtitle">Configure your meme saving preferences</p>
        </div>
        
        <form id="settingsForm">
            <div class="setting-group">
                <h3>🌐 Server Configuration</h3>
                <p>Configure the URLs for your MemeDB installation</p>
                
                <div class="form-group">
                    <label for="memedbUrl">MemeDB Frontend URL:</label>
                    <input type="url" id="memedbUrl" placeholder="http://localhost:3000">
                    <div class="help-text">The URL where your MemeDB frontend is hosted</div>
                </div>
                
                <div class="form-group">
                    <label for="apiUrl">MemeDB API URL:</label>
                    <input type="url" id="apiUrl" placeholder="http://127.0.0.1:3001">
                    <div class="help-text">The URL where your MemeDB backend API is hosted</div>
                </div>
            </div>
            
            <div class="setting-group">
                <h3>⚙️ Behavior Settings</h3>
                <p>Customize how the extension behaves</p>
                
                <label class="checkbox-label">
                    <input type="checkbox" id="openAfterSave">
                    <span>Open MemeDB after saving an image</span>
                </label>
                
                <label class="checkbox-label">
                    <input type="checkbox" id="showNotifications">
                    <span>Show browser notifications</span>
                </label>
                
                <label class="checkbox-label">
                    <input type="checkbox" id="autoTags">
                    <span>Automatically add source website as tag</span>
                </label>
            </div>
            
            <div class="setting-group">
                <h3>🏷️ Default Tags</h3>
                <p>Tags that will be automatically added to saved images</p>
                
                <div class="form-group">
                    <label for="defaultTags">Default Tags (comma-separated):</label>
                    <input type="text" id="defaultTags" placeholder="browser-extension, saved">
                    <div class="help-text">These tags will be added to every saved image</div>
                </div>
            </div>
            
            <button type="submit" class="save-button">💾 Save Settings</button>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>
    
    <script src="options.js"></script>
</body>
</html>
