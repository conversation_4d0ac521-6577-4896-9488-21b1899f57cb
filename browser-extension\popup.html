<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        h1 {
            margin: 0;
            font-size: 1.2rem;
            color: #333;
        }
        
        .subtitle {
            color: #666;
            font-size: 0.9rem;
            margin: 5px 0 0 0;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            text-decoration: none;
            display: block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f8fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #edf2f7;
        }
        
        .instructions {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-size: 0.85rem;
            color: #0c4a6e;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            font-size: 0.9rem;
            color: #0369a1;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.85rem;
            margin: 10px 0;
        }
        
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.85rem;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎭</div>
        <h1>MemeDB Saver</h1>
        <p class="subtitle">Save images to your meme collection</p>
    </div>
    
    <div class="action-buttons">
        <button id="activateFloatingButton" class="btn btn-primary">
            🎯 Activate Floating Button
        </button>
        
        <a href="http://localhost:3000" target="_blank" class="btn btn-secondary">
            🏠 Open MemeDB
        </a>
        
        <button id="openOptions" class="btn btn-secondary">
            ⚙️ Settings
        </button>
    </div>
    
    <div class="instructions">
        <h3>💡 How to use:</h3>
        <ol>
            <li><strong>Right-click</strong> any image → "Save to MemeDB"</li>
            <li><strong>Click the floating button</strong> to activate drag & drop</li>
            <li><strong>Visit MemeDB</strong> to see your saved memes</li>
        </ol>
    </div>
    
    <div id="status" class="status" style="display: none;"></div>
    
    <div class="footer">
        <a href="http://localhost:3000/save-memes.html" target="_blank">
            📚 More Save Methods
        </a>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
