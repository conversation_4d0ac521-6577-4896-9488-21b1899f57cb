{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-sink\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8113656176662020586, "path": 6732125794486852351, "deps": [[1615478164327904835, "pin_utils", false, 2021630172505987135], [1906322745568073236, "pin_project_lite", false, 6019999432545593805], [5451793922601807560, "slab", false, 17237553471217665136], [7013762810557009322, "futures_sink", false, 3804546167481837423], [7620660491849607393, "futures_core", false, 334217847214782913], [16240732885093539806, "futures_task", false, 6124374860355283912]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-188d2b45aec67c3b\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}