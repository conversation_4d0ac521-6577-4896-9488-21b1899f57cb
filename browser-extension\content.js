// Content script for MemeDB browser extension
// This script runs on all web pages

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveImageResult') {
    showNotification(request.message, request.success ? '#10b981' : '#ef4444');
  }
});

// Function to show notifications
function showNotification(text, color = '#10b981') {
  // Remove any existing notifications
  const existingNotifications = document.querySelectorAll('.memedb-notification');
  existingNotifications.forEach(notification => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  });
  
  const notification = document.createElement('div');
  notification.className = 'memedb-notification';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: ${color};
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    z-index: 1000000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    max-width: 400px;
    text-align: center;
    animation: slideDown 0.3s ease-out;
  `;
  notification.innerHTML = text;
  
  // Add slide animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideDown {
      from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
      to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (document.body.contains(notification)) {
      notification.style.animation = 'slideDown 0.3s ease-out reverse';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }
  }, 4000);
}
