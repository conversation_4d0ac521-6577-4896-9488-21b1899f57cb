{"rustc": 1842507548689473721, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"pkg-config\", \"unlock_notify\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"min_sqlite_version_3_6_23\", \"min_sqlite_version_3_6_8\", \"min_sqlite_version_3_7_16\", \"min_sqlite_version_3_7_7\", \"openssl-sys\", \"pkg-config\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 2511973346261130195, "profile": 2241668132362809309, "path": 6426765735284113198, "deps": [[11234225140399302339, "build_script_build", false, 9532033034370150797]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-386c167136118eda\\dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}