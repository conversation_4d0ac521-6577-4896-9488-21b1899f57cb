{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 14160162848842265298, "profile": 2241668132362809309, "path": 1584874732795172919, "deps": [[8081351675046095464, "lock_api", false, 8046433600699693396], [14196108479452351812, "instant", false, 4398115223816454886], [14814334185036658946, "parking_lot_core", false, 1209198875881824280]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot-3638c295496dcf65\\dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}