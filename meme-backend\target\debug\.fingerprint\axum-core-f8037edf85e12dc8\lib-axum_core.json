{"rustc": 1842507548689473721, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 9298926922840548624, "deps": [[784494742817713399, "tower_service", false, 4481196822094599259], [1906322745568073236, "pin_project_lite", false, 8750437579528407050], [2517136641825875337, "sync_wrapper", false, 12415110441443173282], [7712452662827335977, "tower_layer", false, 15900900908958968719], [7858942147296547339, "rustversion", false, 9226041586106900471], [8606274917505247608, "tracing", false, 17408597844338799234], [9010263965687315507, "http", false, 5517233306533306193], [10229185211513642314, "mime", false, 10285210086799124042], [10629569228670356391, "futures_util", false, 3595607879388782086], [11946729385090170470, "async_trait", false, 960252366399444738], [14084095096285906100, "http_body", false, 2746377842349099504], [16066129441945555748, "bytes", false, 3532115715604577537], [16900715236047033623, "http_body_util", false, 11226933646814134663]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-f8037edf85e12dc8\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}