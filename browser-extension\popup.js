// Popup script for MemeDB browser extension

document.addEventListener('DOMContentLoaded', function() {
  const activateButton = document.getElementById('activateFloatingButton');
  const openOptionsButton = document.getElementById('openOptions');
  const statusDiv = document.getElementById('status');
  
  // Activate floating button
  activateButton.addEventListener('click', async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Inject the floating button script
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: injectFloatingButton
      });
      
      showStatus('🎯 Floating button activated!', 'success');
      
      // Close popup after a short delay
      setTimeout(() => {
        window.close();
      }, 1500);
      
    } catch (error) {
      console.error('Error activating floating button:', error);
      showStatus('❌ Failed to activate button', 'error');
    }
  });
  
  // Open options page
  openOptionsButton.addEventListener('click', () => {
    chrome.runtime.openOptionsPage();
  });
  
  // Show status message
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 3000);
  }
});

// Function to inject floating button (will be executed in the page context)
function injectFloatingButton() {
  // Check if button already exists
  if (document.getElementById('memedb-floating-button')) {
    const existing = document.getElementById('memedb-floating-button');
    existing.style.animation = 'bounce 0.5s ease-in-out';
    setTimeout(() => existing.style.animation = '', 500);
    return;
  }
  
  // Create floating button
  const button = document.createElement('div');
  button.id = 'memedb-floating-button';
  button.innerHTML = '🎭 MemeDB';
  button.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 999999;
    cursor: move;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    border: 3px solid transparent;
    transition: all 0.3s ease;
    user-select: none;
    text-align: center;
    min-width: 100px;
  `;
  
  // Add CSS animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes bounce {
      0%, 20%, 60%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      80% { transform: translateY(-5px); }
    }
    @keyframes glow {
      0%, 100% { border-color: #667eea; }
      50% { border-color: #764ba2; }
    }
  `;
  document.head.appendChild(style);
  
  // Make button draggable
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };

  button.addEventListener('mousedown', (e) => {
    isDragging = true;
    dragOffset.x = e.clientX - button.offsetLeft;
    dragOffset.y = e.clientY - button.offsetTop;
    button.style.cursor = 'grabbing';
    e.preventDefault();
  });

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      button.style.left = (e.clientX - dragOffset.x) + 'px';
      button.style.top = (e.clientY - dragOffset.y) + 'px';
      button.style.right = 'auto';
    }
  });

  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      button.style.cursor = 'move';
    }
  });

  // Set up drag and drop functionality
  button.addEventListener('dragover', (e) => {
    e.preventDefault();
    button.style.border = '3px solid #10b981';
    button.style.animation = 'glow 1s infinite';
    button.innerHTML = '🎯 Drop Here!';
  });

  button.addEventListener('dragleave', (e) => {
    e.preventDefault();
    button.style.border = '3px solid transparent';
    button.style.animation = '';
    button.innerHTML = '🎭 MemeDB';
  });

  button.addEventListener('drop', async (e) => {
    e.preventDefault();
    button.style.border = '3px solid transparent';
    button.style.animation = '';
    button.innerHTML = '🎭 MemeDB';

    const imageUrl = e.dataTransfer.getData('text/html');
    const imageUrlMatch = imageUrl.match(/<img[^>]+src="([^">]+)"/);
    
    if (imageUrlMatch) {
      const imgSrc = imageUrlMatch[1];
      // Send message to background script to save the image
      chrome.runtime.sendMessage({
        action: 'saveImage',
        imageUrl: imgSrc,
        sourceUrl: window.location.href,
        title: document.title
      });
    } else {
      const urlData = e.dataTransfer.getData('text/uri-list') || e.dataTransfer.getData('text/plain');
      if (urlData && (urlData.includes('.jpg') || urlData.includes('.png') || urlData.includes('.gif') || urlData.includes('.webp'))) {
        chrome.runtime.sendMessage({
          action: 'saveImage',
          imageUrl: urlData,
          sourceUrl: window.location.href,
          title: document.title
        });
      } else {
        showNotification('❌ Please drag an image!');
      }
    }
  });
  
  // Function to show notifications
  function showNotification(text, color = '#10b981') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${color};
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 20px rgba(0,0,0,0.2);
      max-width: 400px;
      text-align: center;
    `;
    notification.innerHTML = text;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (document.body.contains(notification)) {
        document.body.removeChild(notification);
      }
    }, 4000);
  }

  // Add close functionality
  button.addEventListener('dblclick', () => {
    if (confirm('Remove MemeDB button?')) {
      document.body.removeChild(button);
    }
  });

  // Add to page
  document.body.appendChild(button);
  
  // Show initial notification
  showNotification('🎭 MemeDB button ready! Drag images onto it to save them.', '#667eea');
}
