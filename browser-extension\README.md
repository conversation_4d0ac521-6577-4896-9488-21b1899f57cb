# 🎭 MemeDB Browser Extension

A Chrome/Edge browser extension that makes it super easy to save images to your MemeDB collection.

## ✨ Features

- **Right-click context menu** - Right-click any image → "Save to MemeDB"
- **Floating save button** - Click extension icon to activate drag & drop
- **Automatic tagging** - Auto-tags with source website
- **Customizable settings** - Configure API URLs and behavior
- **Visual feedback** - Success/error notifications
- **PWA integration** - Works with your existing MemeDB installation

## 🚀 Installation

### For Development/Testing:

1. **Open Chrome/Edge** and go to `chrome://extensions/`
2. **Enable Developer Mode** (toggle in top-right)
3. **Click "Load unpacked"** and select the `browser-extension` folder
4. **Pin the extension** to your toolbar for easy access

### For Production:

1. **Zip the extension folder** (excluding README.md)
2. **Upload to Chrome Web Store** or **Edge Add-ons Store**
3. **Users install** from the store

## 🎯 How to Use

### Method 1: Right-Click Context Menu
1. Right-click any image on any website
2. Select "Save to MemeDB" from the context menu
3. Image is automatically saved to your collection

### Method 2: Floating Button
1. Click the MemeDB extension icon in your toolbar
2. A floating "🎭 MemeDB" button appears on the page
3. Drag any image onto the floating button
4. Image is saved automatically

### Method 3: Extension Popup
1. Click the extension icon to open the popup
2. Use the "Activate Floating Button" option
3. Access settings and quick links

## ⚙️ Configuration

Click the extension icon → "Settings" to configure:

- **Server URLs** - Set your MemeDB frontend and API URLs
- **Behavior** - Choose whether to open MemeDB after saving
- **Notifications** - Enable/disable browser notifications
- **Auto-tagging** - Automatically tag with source website
- **Default tags** - Tags added to every saved image

## 🔧 Default Settings

```json
{
  "memedbUrl": "http://localhost:3000",
  "apiUrl": "http://127.0.0.1:3001",
  "openAfterSave": false,
  "showNotifications": true,
  "autoTags": true,
  "defaultTags": "browser-extension, saved"
}
```

## 📁 File Structure

```
browser-extension/
├── manifest.json          # Extension manifest (v3)
├── background.js          # Background service worker
├── content.js            # Content script (runs on all pages)
├── content.css           # Content script styles
├── popup.html            # Extension popup interface
├── popup.js              # Popup functionality
├── options.html          # Settings page
├── options.js            # Settings functionality
├── icons/                # Extension icons (16, 32, 48, 128px)
└── README.md             # This file
```

## 🔒 Permissions

The extension requests these permissions:

- **contextMenus** - Add right-click menu items
- **activeTab** - Access current tab for floating button
- **storage** - Save user settings
- **notifications** - Show save confirmations
- **host_permissions** - Access to your MemeDB URLs

## 🐛 Troubleshooting

### Extension not working?
1. Check that MemeDB backend is running on the configured port
2. Verify API URL in extension settings
3. Check browser console for error messages

### Images not saving?
1. Ensure MemeDB API is accessible
2. Check CORS settings on your backend
3. Verify the image URL is accessible

### Context menu not appearing?
1. Refresh the page after installing the extension
2. Make sure you're right-clicking on an actual image
3. Check that the extension is enabled

## 🔄 Updates

To update the extension during development:
1. Make your changes
2. Go to `chrome://extensions/`
3. Click the refresh icon on the MemeDB Saver extension

## 📦 Publishing

### Chrome Web Store:
1. Create a developer account
2. Zip the extension files
3. Upload and fill out store listing
4. Submit for review

### Edge Add-ons:
1. Create a Microsoft Partner Center account
2. Package the extension
3. Submit through Partner Center

## 🤝 Contributing

1. Fork the repository
2. Make your changes
3. Test thoroughly
4. Submit a pull request

## 📄 License

Same license as the main MemeDB project.

---

**Made with ❤️ for the MemeDB community**
