{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"once_cell\", \"runtime-tokio-rustls\", \"tokio\", \"tokio-rustls\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-native-tls\", \"async-std\", \"futures-rustls\", \"native-tls\", \"once_cell\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"tokio\", \"tokio-native-tls\", \"tokio-rustls\"]", "target": 15562956346104333444, "profile": 2225463790103693989, "path": 5900591576528155179, "deps": [[697264297314089779, "tokio_rustls", false, 12847386446912914971], [3722963349756955755, "once_cell", false, 10849160098691136940], [12393800526703971956, "tokio", false, 617748297967758338]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-rt-d6a4195cb9de7ef2\\dep-lib-sqlx_rt", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}