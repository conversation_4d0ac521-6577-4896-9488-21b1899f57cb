{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"crc\", \"flume\", \"futures-executor\", \"libsqlite3-sys\", \"migrate\", \"runtime-tokio-rustls\", \"rustls\", \"rustls-pemfile\", \"sha2\", \"sqlite\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"all-databases\", \"all-types\", \"any\", \"base64\", \"bigdecimal\", \"bigdecimal_\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"decimal\", \"default\", \"digest\", \"dirs\", \"encoding_rs\", \"flume\", \"futures-executor\", \"generic-array\", \"git2\", \"hkdf\", \"hmac\", \"ipnetwork\", \"json\", \"libsqlite3-sys\", \"mac_address\", \"md-5\", \"migrate\", \"mssql\", \"mysql\", \"num-bigint\", \"offline\", \"postgres\", \"rand\", \"regex\", \"rsa\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"sqlite\", \"time\", \"tokio-stream\", \"uuid\", \"webpki-roots\", \"whoami\"]", "target": 2042750936636613814, "profile": 2225463790103693989, "path": 6199976944723520646, "deps": [[40386456601120721, "percent_encoding", false, 13311225380363587709], [530211389790465181, "hex", false, 16660057562933494330], [788558663644978524, "crossbeam_queue", false, 1071184393890480504], [1162433738665300155, "crc", false, 15160278120153225370], [1464803193346256239, "event_listener", false, 8429155471222120813], [1526817731016152233, "stringprep", false, 6234965733653605539], [1669656952637834339, "atoi", false, 3127292149716513996], [1811549171721445101, "futures_channel", false, 5961310731824396758], [3150220818285335163, "url", false, 18246970257470212370], [3405707034081185165, "dotenvy", false, 11776773280536702065], [3405817021026194662, "hashlink", false, 6237986475843858661], [3666196340704888985, "smallvec", false, 8738050254830304838], [3712811570531045576, "byteorder", false, 15841746677002091313], [3722963349756955755, "once_cell", false, 10849160098691136940], [4684437522915235464, "libc", false, 6165958497664875510], [5453111487848273541, "futures_intrusive", false, 6593734028825913882], [5986029879202738730, "log", false, 6009918710125207883], [7620660491849607393, "futures_core", false, 334217847214782913], [7695812897323945497, "itoa", false, 6802696783076626873], [8008191657135824715, "thiserror", false, 1945450773848843622], [9857275760291862238, "sha2", false, 2256126064678007473], [10435729446543529114, "bitflags", false, 18233786339933947246], [10629569228670356391, "futures_util", false, 13482602704915301129], [10862088793507253106, "sqlformat", false, 3722660893850522758], [11234225140399302339, "libsqlite3_sys", false, 12831221138414957335], [12170264697963848012, "either", false, 7810909085640143695], [12779779637805422465, "futures_executor", false, 16030013524319772614], [13879594675704253779, "flume", false, 17520733675220623521], [14138448670229598068, "webpki_roots", false, 10825994970803704737], [14923790796823607459, "indexmap", false, 13107244755250633158], [15733757238428312503, "rustls", false, 13525601660757989024], [15932120279885307830, "memchr", false, 14881734322735361218], [16066129441945555748, "bytes", false, 5589847916839953707], [16311359161338405624, "rustls_pemfile", false, 18066386297044369219], [16973251432615581304, "tokio_stream", false, 2844681287144384766], [17055994635158723026, "sqlx_rt", false, 1782016545619217520], [17605717126308396068, "paste", false, 10225067998843437207], [18195555696463914673, "ahash", false, 10119057254036914742]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-80b16bdf3a9e9683\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}